package com.tqhit.battery.one.features.stats.health.repository

import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.manager.graph.HistoryEntry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Dedicated repository for battery history data collection specifically for health fragment.
 * Integrates with CoreBatteryStatsService to collect real-time battery and temperature data.
 * 
 * This repository follows the stats module architecture pattern and provides clean separation
 * of concerns for health-specific data handling.
 */
@Singleton
class HistoryBatteryRepository @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    
    companion object {
        private const val TAG = "HistoryBatteryRepository"
        private const val HISTORY_ENTRY_INTERVAL_MS = 60_000L // 1 minute
        private const val MAX_HISTORY_ENTRIES = 1440 // 24 hours worth of minute entries
    }
    
    // Coroutine scope for background data collection
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Thread-safe collections for storing history data
    private val batteryHistory = ConcurrentLinkedQueue<HistoryEntry<Int>>()
    private val temperatureHistory = ConcurrentLinkedQueue<HistoryEntry<Double>>()
    
    // Track last entry timestamps to avoid duplicate entries
    private var lastBatteryEntryTime = 0L
    private var lastTemperatureEntryTime = 0L
    
    init {
        startBatteryDataCollection()
    }
    
    /**
     * Starts collecting battery data from CoreBatteryStatsService.
     * Processes CoreBatteryStatus updates and stores historical data.
     */
    private fun startBatteryDataCollection() {
        repositoryScope.launch {
            Log.d(TAG, "PRODUCTION_TEST: Starting battery data collection for health fragment")
            
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .collect { coreStatus ->
                    Log.d(TAG, "CHART_DATA: === RECEIVED CORE BATTERY STATUS ===")
                    Log.d(TAG, "CHART_DATA: Status details:")
                    Log.d(TAG, "CHART_DATA:   Percentage: ${coreStatus.percentage}%")
                    Log.d(TAG, "CHART_DATA:   Temperature: ${coreStatus.temperatureCelsius}°C")
                    Log.d(TAG, "CHART_DATA:   Charging: ${coreStatus.isCharging}")
                    Log.d(TAG, "CHART_DATA:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(coreStatus.timestampEpochMillis))}")

                    val currentTime = coreStatus.timestampEpochMillis
                    val timeSinceLastBattery = currentTime - lastBatteryEntryTime
                    val timeSinceLastTemp = currentTime - lastTemperatureEntryTime

                    Log.d(TAG, "CHART_DATA: Time since last entries - Battery: ${timeSinceLastBattery}ms, Temp: ${timeSinceLastTemp}ms")
                    Log.d(TAG, "CHART_DATA: Interval threshold: ${HISTORY_ENTRY_INTERVAL_MS}ms")

                    // Add battery percentage entry if enough time has passed
                    if (timeSinceLastBattery >= HISTORY_ENTRY_INTERVAL_MS) {
                        Log.d(TAG, "CHART_DATA: ✅ Adding battery history entry (interval met)")
                        addBatteryHistoryEntry(currentTime, coreStatus.percentage)
                        lastBatteryEntryTime = currentTime
                    } else {
                        Log.d(TAG, "CHART_DATA: ⏳ Skipping battery entry (interval not met: ${timeSinceLastBattery}ms < ${HISTORY_ENTRY_INTERVAL_MS}ms)")
                    }

                    // Add temperature entry if enough time has passed
                    if (timeSinceLastTemp >= HISTORY_ENTRY_INTERVAL_MS) {
                        Log.d(TAG, "CHART_DATA: ✅ Adding temperature history entry (interval met)")
                        addTemperatureHistoryEntry(currentTime, coreStatus.temperatureCelsius.toDouble())
                        lastTemperatureEntryTime = currentTime
                    } else {
                        Log.d(TAG, "CHART_DATA: ⏳ Skipping temperature entry (interval not met: ${timeSinceLastTemp}ms < ${HISTORY_ENTRY_INTERVAL_MS}ms)")
                    }

                    Log.d(TAG, "CHART_DATA: Current collection sizes - Battery: ${batteryHistory.size}, Temperature: ${temperatureHistory.size}")
                }
        }
    }
    
    /**
     * Adds a battery percentage history entry.
     */
    private fun addBatteryHistoryEntry(timestamp: Long, percentage: Int) {
        val entry = HistoryEntry(timestamp, percentage)
        batteryHistory.add(entry)

        // Remove old entries to maintain size limit
        val removedEntries = mutableListOf<HistoryEntry<Int>>()
        while (batteryHistory.size > MAX_HISTORY_ENTRIES) {
            batteryHistory.poll()?.let { removedEntries.add(it) }
        }

        Log.d(TAG, "CHART_DATA: ➕ BATTERY ENTRY ADDED")
        Log.d(TAG, "CHART_DATA:   Percentage: $percentage%")
        Log.d(TAG, "CHART_DATA:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(timestamp))}")
        Log.d(TAG, "CHART_DATA:   Total entries: ${batteryHistory.size}")
        if (removedEntries.isNotEmpty()) {
            Log.d(TAG, "CHART_DATA:   Removed ${removedEntries.size} old entries to maintain limit")
        }
        Log.d(TAG, "CHART_DATA: This should trigger HealthFragment chart updates")
    }
    
    /**
     * Adds a temperature history entry.
     */
    private fun addTemperatureHistoryEntry(timestamp: Long, temperature: Double) {
        val entry = HistoryEntry(timestamp, temperature)
        temperatureHistory.add(entry)

        // Remove old entries to maintain size limit
        val removedEntries = mutableListOf<HistoryEntry<Double>>()
        while (temperatureHistory.size > MAX_HISTORY_ENTRIES) {
            temperatureHistory.poll()?.let { removedEntries.add(it) }
        }

        Log.d(TAG, "CHART_DATA: ➕ TEMPERATURE ENTRY ADDED")
        Log.d(TAG, "CHART_DATA:   Temperature: $temperature°C")
        Log.d(TAG, "CHART_DATA:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(timestamp))}")
        Log.d(TAG, "CHART_DATA:   Total entries: ${temperatureHistory.size}")
        if (removedEntries.isNotEmpty()) {
            Log.d(TAG, "CHART_DATA:   Removed ${removedEntries.size} old entries to maintain limit")
        }
        Log.d(TAG, "CHART_DATA: This should trigger HealthFragment chart updates")
    }
    
    /**
     * Gets battery percentage history for the specified number of hours.
     * 
     * @param hours Number of hours of history to retrieve
     * @return List of battery percentage history entries
     */
    fun getHistoryBatteryForHours(hours: Int): List<HistoryEntry<Int>> {
        val cutoffTime = System.currentTimeMillis() - (hours * 60 * 60 * 1000L)
        val filteredEntries = batteryHistory.filter { it.timestamp >= cutoffTime }
        
        Log.d(TAG, "PRODUCTION_TEST: Retrieved ${filteredEntries.size} battery entries for ${hours}h (cutoff: $cutoffTime)")
        
        return filteredEntries.toList()
    }
    
    /**
     * Gets temperature history for the specified number of hours.
     * 
     * @param hours Number of hours of history to retrieve
     * @return List of temperature history entries
     */
    fun getHistoryTemperatureForHours(hours: Int): List<HistoryEntry<Double>> {
        val cutoffTime = System.currentTimeMillis() - (hours * 60 * 60 * 1000L)
        val filteredEntries = temperatureHistory.filter { it.timestamp >= cutoffTime }
        
        Log.d(TAG, "PRODUCTION_TEST: Retrieved ${filteredEntries.size} temperature entries for ${hours}h (cutoff: $cutoffTime)")
        
        return filteredEntries.toList()
    }
    
    /**
     * Gets daily wear data (placeholder implementation for compatibility).
     * In a real implementation, this would calculate battery wear based on charge cycles.
     * 
     * @param days Number of days of wear data to retrieve
     * @return List of daily wear percentages
     */
    fun getDailyWearData(days: Int): List<Double> {
        // For now, return empty list since this is primarily used for sample data
        // Real implementation would calculate wear based on charging sessions
        Log.d(TAG, "PRODUCTION_TEST: getDailyWearData called for $days days - returning empty list")
        return emptyList()
    }
    
    /**
     * Gets the current number of battery history entries.
     * Useful for debugging and monitoring data collection.
     */
    fun getBatteryHistoryCount(): Int = batteryHistory.size
    
    /**
     * Gets the current number of temperature history entries.
     * Useful for debugging and monitoring data collection.
     */
    fun getTemperatureHistoryCount(): Int = temperatureHistory.size
    
    /**
     * Clears all history data.
     * Useful for testing or resetting data collection.
     */
    fun clearHistory() {
        batteryHistory.clear()
        temperatureHistory.clear()
        lastBatteryEntryTime = 0L
        lastTemperatureEntryTime = 0L
        Log.d(TAG, "PRODUCTION_TEST: History data cleared")
    }
}
